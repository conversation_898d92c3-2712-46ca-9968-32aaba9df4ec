# Dart Scorer Pro - Optimized Windows Build

## 🎯 Features
- **Offline-Only**: No Firebase dependency, 100% local storage
- **Windows Optimized**: Built specifically for Windows x64
- **Compact Size**: ~256 MB (vs 300+ MB original)
- **Portable**: No installation required

## 🚀 How to Run
1. Double-click "Start Dart Scorer Pro.bat"
2. Or run: node_modules\.bin\electron.cmd .

## 📊 Size Comparison
- **Original Build**: 300+ MB (Firebase + cross-platform)
- **Optimized Build**: ~256 MB (offline-only + Windows x64)
- **Size Reduction**: ~15% smaller + removed 20MB Firebase

## 🔧 Optimizations Applied
- ✅ Removed Firebase SDK (~20 MB)
- ✅ Windows x64 only (no Mac/Linux binaries)
- ✅ Maximum compression enabled
- ✅ Removed development dependencies
- ✅ Offline storage only
- ✅ Removed cross-platform build tools

## 💾 Storage
- Uses browser localStorage for game saves
- Export/import functionality included
- No internet connection required

## 🎮 Game Features
- X01 games (301, 501, 701, custom)
- Interactive dartboard
- Manual dart entry
- Save/load games
- Player statistics
- Checkout suggestions
- Undo functionality

Built with ❤️ for Windows dart players!