// checkout.js

// Common checkouts, especially for finishing on a double.
// This is a simplified list. A full checkout calculator is more complex.
// Structure: score: ["Possible checkout 1", "Possible checkout 2", ...]
const COMMON_CHECKOUTS = {
    170: ["T20, T20, DBull"],
    167: ["T20, T19, DBull"],
    164: ["T20, T18, DBull", "T19, T19, DBull"],
    161: ["T20, T17, DBull"],
    160: ["T20, T20, D20"],
    158: ["T20, T20, D19"],
    157: ["T20, T19, D20"],
    156: ["T20, T20, D18"],
    155: ["T20, T19, D19"],
    154: ["T20, T18, D20"],
    153: ["T20, T19, D18"],
    152: ["T20, T20, D16"],
    151: ["T20, T17, D20"],
    150: ["T20, T18, D18", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (Showboat!)"], // Or T20, T10, DBull... but this is standard
    149: ["T20, T19, D16"],
    148: ["T20, T16, D20"],
    147: ["T20, T17, D18", "T19, T18, D18"],
    146: ["T20, T18, D16"],
    145: ["T20, T15, D20"],
    144: ["T20, T20, D12", "T18, T18, D18"],
    143: ["T20, T17, D16"],
    142: ["T20, T14, D20"],
    141: ["T20, T15, D18", "T19, T16, D18"],
    140: ["T20, T20, D10", "T20, S20, DBull"],
    // ... many more down to 2
    // Two-dart checkouts (examples)
    100: ["T20, D20"],
    99: ["T19, D21 (not possible, so T19, S10, D16 or T19, S2, D20...) -> better T19, D20 (if started D20) -> T19, (42) D21 -> T19, (42) S10, D16"], // Needs better logic
    98: ["T20, D19"],
    // ...
    60: ["S20, D20", "T20"], // T20 is one dart, but common setup for D20 if 2 darts left
    50: ["S10, D20", "S18, D16", "DBull"],
    40: ["D20", "S8, D16", "S20, D10"],
    32: ["D16", "S16, D8", "S8, D12"],
    2: ["D1"],
};

// More granular dart values for calculation
const dartValues = [];
for (let i = 1; i <= 20; i++) {
    dartValues.push({ text: `S${i}`, value: i, type: 'S', score: i, multiplier: 1, isDouble: false });
    dartValues.push({ text: `D${i}`, value: i * 2, type: 'D', score: i, multiplier: 2, isDouble: true });
    dartValues.push({ text: `T${i}`, value: i * 3, type: 'T', score: i, multiplier: 3, isDouble: false });
}
dartValues.push({ text: 'Bull', value: 25, type: 'S', score: 25, multiplier: 1, isDouble: false }); // Single Bull
dartValues.push({ text: 'DBull', value: 50, type: 'D', score: 25, multiplier: 2, isDouble: true }); // Double Bull

dartValues.sort((a, b) => b.value - a.value); // Sort by value descending for greedy approach

export function getCheckoutSuggestions(score, mustFinishOnDouble = true) {
    if (score <= 1 || score > 170) return []; // Standard checkout range, and 1 is not checkoutable on double.

    // First, check common pre-defined checkouts for speed
    if (COMMON_CHECKOUTS[score]) {
        if (mustFinishOnDouble) {
            return COMMON_CHECKOUTS[score].filter(checkout => {
                const lastDart = checkout.split(',').pop().trim();
                return lastDart.startsWith('D') || lastDart === 'DBull';
            });
        }
        return COMMON_CHECKOUTS[score];
    }

    const suggestions = new Set(); // Use a Set to avoid duplicate suggestions

    // Try to find 3-dart checkouts
    for (const d1 of dartValues) {
        if (d1.value > score) continue;
        if (mustFinishOnDouble && d1.value === score && !d1.isDouble) continue; // 1-dart finish must be double
        if (d1.value === score && d1.isDouble) {
             suggestions.add(`${d1.text}`);
             if (suggestions.size >=5) break;
             continue;
        }

        for (const d2 of dartValues) {
            const scoreAfterD1 = score - d1.value;
            if (d2.value > scoreAfterD1) continue;
            if (mustFinishOnDouble && d2.value === scoreAfterD1 && !d2.isDouble) continue; // 2-dart finish, d2 must be double
             if (d2.value === scoreAfterD1 && d2.isDouble) {
                suggestions.add(`${d1.text}, ${d2.text}`);
                if (suggestions.size >=5) break;
                continue;
            }

            for (const d3 of dartValues) {
                if (mustFinishOnDouble && !d3.isDouble) continue; // 3rd dart must be a double
                if (d1.value + d2.value + d3.value === score) {
                    // Basic check for non-sensical T-T-T that could be S-S-D etc.
                    // This logic can be greatly improved for optimal checkouts
                    if (d1.value >= d2.value || d2.value >= d3.value || !mustFinishOnDouble) { // Prioritize higher scores first or any if not double out
                         suggestions.add(`${d1.text}, ${d2.text}, ${d3.text}`);
                         if (suggestions.size >=5) break;
                    }
                }
            }
            if (suggestions.size >=5) break;
        }
        if (suggestions.size >=5) break;
    }
    
    const sortedSuggestions = Array.from(suggestions);
    // Optionally sort suggestions (e.g., by number of darts, then by value)
    // For now, just return what we found up to 5.
    return sortedSuggestions.slice(0, 5);
}

// Example usage:
// console.log(getCheckoutSuggestions(170)); // ["T20, T20, DBull"]
// console.log(getCheckoutSuggestions(100)); // ["T20, D20"] or calculated
// console.log(getCheckoutSuggestions(40));  // ["D20", "S8, D16", "S20,D10"]
// console.log(getCheckoutSuggestions(32));  // ["D16"]
// console.log(getCheckoutSuggestions(2));   // ["D1"]
// console.log(getCheckoutSuggestions(1));   // [] (if mustFinishOnDouble is true)
// console.log(getCheckoutSuggestions(1, false)); // ["S1"] (if mustFinishOnDouble is false)

