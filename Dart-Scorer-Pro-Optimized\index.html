<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dart Scorer Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            overscroll-behavior: none; /* Prevents pull-to-refresh on some systems */
        }
        /* Custom scrollbar for better aesthetics if needed */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        .dartboard-segment {
            cursor: pointer;
            transition: fill 0.2s ease-in-out, stroke 0.2s ease-in-out;
        }
        .dartboard-segment:hover {
            opacity: 0.8;
        }
        /* Specific styles for dartboard segments, will be applied via JS or directly in SVG */
        .segment-single { fill: #FFFEF7; stroke: #000000; stroke-width:1;} /* Cream/white for light single segments */
        .segment-single-odd { fill: #000000; stroke: #000000; stroke-width:1;} /* Black for dark single segments */
        .segment-double { fill: #DC2626; stroke: #000000; stroke-width:1.5;} /* Red for doubles */
        .segment-triple { fill: #DC2626; stroke: #000000; stroke-width:1.5;} /* Red for triples */
        .segment-bull { fill: #16A34A; stroke: #000000; stroke-width:1.5;} /* Green for outer bull (25) */
        .segment-double-bull { fill: #DC2626; stroke: #000000; stroke-width:1.5;} /* Red for inner bull (50) */
        .segment-miss { fill: #1F4E3D; }
        .dartboard-number {
            fill: #000000;
            font-family: Arial, sans-serif;
            font-weight: bold;
            font-size: 24px;
            text-anchor: middle;
            stroke: #FFFFFF;
            stroke-width: 3;
            paint-order: stroke fill;
        }

        .player-card.active {
            border-color: #FACC15; /* yellow-400 */
            box-shadow: 0 0 15px rgba(250, 204, 21, 0.5);
        }
         #toast-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .toast {
            background-color: #333;
            color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInToast 0.5s forwards, fadeOutToast 0.5s 4.5s forwards;
        }
        @keyframes fadeInToast {
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes fadeOutToast {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(20px); }
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100 flex flex-col items-center justify-center min-h-screen p-4 select-none">

    <div id="app-container" class="w-full max-w-7xl mx-auto">

        <!-- Game Setup Screen -->
        <div id="game-setup-screen" class="bg-gray-800 p-8 rounded-lg shadow-2xl ">
            <h1 class="text-4xl font-bold text-center text-yellow-400 mb-8">Dart Scorer Pro</h1>
            
            <div class="grid md:grid-cols-2 gap-8 mb-8">
                <!-- Players -->
                <div>
                    <h2 class="text-2xl font-semibold text-sky-400 mb-4">Players</h2>
                    <div class="flex gap-2 mb-4">
                        <input type="text" id="new-player-name" placeholder="Enter player name" class="flex-grow bg-gray-700 text-gray-100 placeholder-gray-400 p-3 rounded-md border border-gray-600 focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none">
                        <button id="add-player-btn" class="bg-sky-500 hover:bg-sky-600 text-white font-semibold py-3 px-6 rounded-md transition duration-150">Add Player</button>
                    </div>
                    <div id="player-list" class="space-y-2 max-h-40 overflow-y-auto pr-2">
                        <!-- Player items will be added here -->
                    </div>
                </div>

                <!-- Game Mode Selection -->
                <div>
                    <h2 class="text-2xl font-semibold text-green-400 mb-4">Game Mode</h2>
                    <select id="game-mode-select" class="w-full bg-gray-700 text-gray-100 p-3 rounded-md border border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-green-500 outline-none mb-4">
                        <option value="101">101</option>
                        <option value="201">201</option>
                        <option value="301">301</option>
                        <option value="501" selected>501</option>
                        <option value="701">701</option>
                        <option value="custom">Custom Start Score</option>
                    </select>
                    <input type="number" id="custom-start-score" placeholder="Enter custom start score (e.g., 601)" class="w-full bg-gray-700 text-gray-100 placeholder-gray-400 p-3 rounded-md border border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-green-500 outline-none hidden mb-4" min="2">
                     <div class="mb-4">
                        <label for="finish-on-double-checkbox" class="flex items-center space-x-2 text-gray-300">
                            <input type="checkbox" id="finish-on-double-checkbox" class="form-checkbox h-5 w-5 text-green-500 bg-gray-700 border-gray-600 rounded focus:ring-green-500" checked>
                            <span>Must finish on a Double </span>
                        </label>
                    </div>
                </div>
            </div>
            
            <button id="start-game-btn" class="w-full bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-bold py-4 px-6 rounded-lg text-xl transition duration-150 shadow-md">Start Game</button>
            
            <div class="mt-8">
                <h3 class="text-xl font-semibold text-purple-400 mb-2">Load Game</h3>
                <div class="flex gap-2">
                    <input type="text" id="load-game-id-input" placeholder="Enter Game ID to load" class="flex-grow bg-gray-700 p-3 rounded-md border border-gray-600">
                    <button id="load-game-btn" class="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-4 rounded-md">Load</button>
                </div>
                <div id="saved-games-list" class="mt-2 text-sm text-gray-400"></div>

                <!-- Storage Management Section -->
                <div class="mt-6 pt-4 border-t border-gray-600">
                    <h4 class="text-lg font-semibold text-orange-400 mb-3">Storage Management</h4>
                    <div class="grid grid-cols-2 gap-2">
                        <button id="export-games-btn" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-3 rounded-md transition text-sm disabled:opacity-50" disabled>Export Games</button>
                        <button id="import-games-btn" class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-3 rounded-md transition text-sm disabled:opacity-50" disabled>Import Games</button>
                    </div>
                    <input type="file" id="import-file-input" accept=".json" class="hidden">
                    <div id="storage-info" class="text-xs text-gray-400 mt-2">
                        <!-- Storage info will appear here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Play Screen -->
        <div id="game-play-screen" class="hidden w-full">
            <div class="flex justify-between items-center mb-6">
                <h1 id="game-title" class="text-3xl font-bold text-yellow-400">501 Game</h1>
                <div class="text-xl">
                    <span>Round: <span id="current-round" class="font-semibold text-sky-400">1</span></span>
                </div>
                 <button id="save-game-btn" class="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-md transition duration-150">Save Game</button>
                <button id="quit-game-btn" class="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-md transition duration-150">Quit Game</button>
            </div>
            
            <!-- Scoreboard -->
            <div id="scoreboard" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
                <!-- Player cards will be dynamically inserted here -->
            </div>

            <div class="grid md:grid-cols-3 gap-6">
                <!-- Dartboard and Score Input -->
                <div class="md:col-span-2 bg-gray-800 p-6 rounded-lg shadow-xl">
                    <h2 id="current-player-turn-indicator" class="text-2xl font-semibold text-center text-yellow-400 mb-1">Player's Turn</h2>
                    <p id="player-checkout-message" class="text-center text-sky-300 mb-4 h-6"></p>
                    
                    <!-- Visual Dartboard -->
                    <div id="dartboard-container" class="aspect-square max-w-md mx-auto mb-4 relative">
                        <!-- SVG Dartboard will be injected here by dartboard.js -->
                        <svg id="interactive-dartboard" viewBox="0 0 600 600" class="w-full h-full">
                           <!-- Dartboard segments will be generated by JS -->
                        </svg>
                    </div>

                    <!-- Manual Score Input -->
                    <div class="flex items-center gap-2 mb-4">
                        <input type="text" id="manual-dart-input" placeholder="e.g., T20, D16, S5, Bull, DB" class="flex-grow bg-gray-700 text-gray-100 p-3 rounded-md border border-gray-600 focus:ring-sky-500">
                        <button id="submit-manual-dart-btn" class="bg-sky-500 hover:bg-sky-600 text-white font-semibold py-3 px-4 rounded-md">Enter Dart</button>
                    </div>
                    
                    <!-- Current Turn Display -->
                    <div class="bg-gray-700 p-4 rounded-md mb-4">
                        <h3 class="text-lg font-semibold text-gray-300 mb-2">Current Turn Darts:</h3>
                        <div id="current-turn-darts-display" class="flex justify-around text-xl h-8 mb-1">
                            <!-- Dart 1, Dart 2, Dart 3 scores shown here -->
                        </div>
                        <div class="text-right">
                             <button id="undo-last-dart-btn" class="text-sm bg-yellow-600 hover:bg-yellow-700 text-white py-1 px-3 rounded-md mr-2 disabled:opacity-50" disabled>Undo Dart</button>
                            Turn Total: <span id="current-turn-total-score" class="font-bold text-2xl text-green-400">0</span>
                        </div>
                    </div>

                    <div class="flex gap-4">
                        <button id="next-player-btn" class="flex-1 bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg text-lg transition disabled:opacity-50" disabled>Next Player / Confirm Turn</button>
                        <button id="bust-btn" class="flex-1 bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg text-lg transition disabled:opacity-50" disabled>Bust</button>
                    </div>
                </div>

                <!-- Game Log and Controls -->
                <div class="bg-gray-800 p-6 rounded-lg shadow-xl">
                    <h2 class="text-xl font-semibold text-purple-400 mb-3">Game Log</h2>
                    <div id="game-log" class="h-64 overflow-y-auto bg-gray-700 p-3 rounded-md mb-4 text-sm space-y-1">
                        <!-- Log entries will appear here -->
                    </div>
                    <h2 class="text-xl font-semibold text-orange-400 mb-3">Game Controls</h2>
                    <button id="undo-last-turn-btn" class="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded-md mb-2 transition disabled:opacity-50" disabled>Undo Last Turn</button>
                     <div id="checkout-helper-container" class="mt-4">
                        <h3 class="text-lg font-semibold text-teal-400 mb-2">Checkout Suggestions:</h3>
                        <div id="checkout-suggestions" class="text-sm text-gray-300 space-y-1 h-24 overflow-y-auto">
                            <!-- Suggestions appear here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Winner Modal -->
        <div id="winner-modal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 hidden z-50">
            <div class="bg-gray-800 p-8 md:p-12 rounded-xl shadow-2xl text-center max-w-md w-full transform transition-all scale-95 opacity-0" id="winner-modal-content">
                <h2 class="text-5xl font-bold text-yellow-400 mb-4">WINNER!</h2>
                <p id="winner-name" class="text-3xl text-white mb-2"></p>
                <p id="winner-details" class="text-lg text-gray-300 mb-6"></p>
                <div id="winner-stats" class="text-left text-gray-400 mb-6 space-y-1">
                    <!-- Winner stats will be shown here -->
                </div>
                <div class="flex gap-4">
                    <button id="play-again-btn" class="flex-1 bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg text-lg">Play Again (Same Settings)</button>
                    <button id="new-game-setup-btn" class="flex-1 bg-sky-500 hover:bg-sky-600 text-white font-bold py-3 px-6 rounded-lg text-lg">New Game Setup</button>
                </div>
            </div>
        </div>
    </div>
    <div id="toast-container"></div> <!-- For brief messages -->
    <div id="user-id-display" class="fixed bottom-2 left-2 text-xs text-gray-500">UserID: <span id="user-id-value"></span></div>

    <!-- Offline-Only Build - Firebase removed for size optimization -->
    
    <!-- App Scripts -->
    <script type="module" src="./renderer.js"></script>
    <!-- Individual modules will be imported by renderer.js -->
</body>
</html>
