# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.4](https://github.com/es-shims/globalThis/compare/v1.0.3...v1.0.4) - 2024-04-29

### Commits

- [actions] remove redundant finisher [`280d796`](https://github.com/es-shims/globalThis/commit/280d796f7cd61da47c026d8ec8dd88015d4ed95f)
- [Refactor] use `gopd` [`0209ccb`](https://github.com/es-shims/globalThis/commit/0209ccb2cd95b785e7e8868fab035cdc87216b58)
- [actions] update rebase action to use reusable workflow [`c08aea6`](https://github.com/es-shims/globalThis/commit/c08aea6240c3747cbc3e5f4d7c3eb740ec4f0627)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`f38f2af`](https://github.com/es-shims/globalThis/commit/f38f2af14797abbe466b428f0ce74843c43746d7)
- [Dev Deps] update `aud`, `tape` [`a1be102`](https://github.com/es-shims/globalThis/commit/a1be102c91da38830a45804de6a0582f752fe53f)
- [Deps] update `define-properties` [`3e41644`](https://github.com/es-shims/globalThis/commit/3e416444f87350a6df70bf778e95eb713c3011e6)
- [Deps] update `define-properties` [`3d81f70`](https://github.com/es-shims/globalThis/commit/3d81f7048ce35285e3e719b1f53fba02516e9811)
- [Dev Deps] add missing `npmignore` dep [`c2d00f7`](https://github.com/es-shims/globalThis/commit/c2d00f70d4c11cb2f035c398cb560db9677b6dc6)

## [v1.0.3](https://github.com/es-shims/globalThis/compare/v1.0.2...v1.0.3) - 2022-05-07

### Commits

- [actions] reuse common workflows [`65891e4`](https://github.com/es-shims/globalThis/commit/65891e4d285ae04e216ff01160cff861e0e41a4f)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`82f8481`](https://github.com/es-shims/globalThis/commit/82f84815027f666f625e1ccb41f723800a05d016)
- [meta] use `npmignore` to autogenerate an npmignore file [`53afc39`](https://github.com/es-shims/globalThis/commit/53afc39bfd3eb262c5e6e9dfd25e4f81f3578c1c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`03169d4`](https://github.com/es-shims/globalThis/commit/03169d4254c9ef177d6537becca5b0b56df50d91)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `tape` [`4986e3e`](https://github.com/es-shims/globalThis/commit/4986e3e20c5f664601871a0fac68c1efd0a68472)
- [actions] update codecov uploader [`15c4b06`](https://github.com/es-shims/globalThis/commit/15c4b062b1a9434dbec93604ed31b6893d11d458)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `tape` [`8b04a74`](https://github.com/es-shims/globalThis/commit/8b04a749d3cb2f825920beb700899f0c13ad2fb8)
- [Fix] `globalThis` should be writable [`8759985`](https://github.com/es-shims/globalThis/commit/87599852d5f91e2e1f06e424cdefcd443ec98476)
- [readme] add github actions/codecov badges [`0263f0d`](https://github.com/es-shims/globalThis/commit/0263f0debfa982b928fcd301b11fe3e3193bf33d)
- [Dev Deps] update `aud`, `eslint`, `tape` [`e88d296`](https://github.com/es-shims/globalThis/commit/e88d296bb026633bdd1be2e1542903a5d0107cd8)
- [meta] use `prepublishOnly` script for npm 7+ [`c81fde6`](https://github.com/es-shims/globalThis/commit/c81fde6a9e44345e56dada588e16db736809ddd9)
- [Tests] nycignore `dist` [`bde0c0d`](https://github.com/es-shims/globalThis/commit/bde0c0df46f684316ab414da1487a0cd2efe3eeb)
- [meta] gitignore coverage output [`79f73f8`](https://github.com/es-shims/globalThis/commit/79f73f8b0c1180567fba473f92c07d71efd4dd0b)

## [v1.0.2](https://github.com/es-shims/globalThis/compare/v1.0.1...v1.0.2) - 2021-02-22

### Commits

- [Tests] migrate tests to Github Actions [`a3f50f7`](https://github.com/es-shims/globalThis/commit/a3f50f77a392c0ffdaca18fb5881743b874d0a6f)
- [meta] do not publish github action workflow files [`eb5c787`](https://github.com/es-shims/globalThis/commit/eb5c7879317cd7f1fde52228660be8e779c9d4e3)
- [Tests] add `implementation` est; run `es-shim-api` in postlint; use `tape` runner [`c9dd792`](https://github.com/es-shims/globalThis/commit/c9dd792d492ec9744a5e5d5033e919b94d441bac)
- [Tests] fix native tests [`6b76dff`](https://github.com/es-shims/globalThis/commit/6b76dff3af3fe9bcd7b24d48c6ba55116169e840)
- [Tests] run `nyc` on all tests [`0407f79`](https://github.com/es-shims/globalThis/commit/0407f79f64bf9fc30111f3bf4dff7e4205331fb6)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape`, `browserify` [`b8cc020`](https://github.com/es-shims/globalThis/commit/b8cc020e5ecc2d5a5a5b4160aabc60cc42d50c03)
- [actions] add "Allow Edits" workflow [`e2854df`](https://github.com/es-shims/globalThis/commit/e2854df653667b16ff34a7a0a7b677231dfe2b02)
- [readme] remove travis badge [`262eb76`](https://github.com/es-shims/globalThis/commit/262eb76e4e0d3f2df354cc6aff1b18f50c7b147f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`; add `safe-publish-latest` [`3c76883`](https://github.com/es-shims/globalThis/commit/3c7688325f6aa050afe3ed978e423e70974e4d3b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`7276123`](https://github.com/es-shims/globalThis/commit/727612396262fc22275f44159ec5b39115dc359f)
- [actions] update workflows [`bcb0f42`](https://github.com/es-shims/globalThis/commit/bcb0f42c319cf19746e03a6667cf25d3e835f46e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`5485851`](https://github.com/es-shims/globalThis/commit/548585148e874d6eb0b0463526a88e8b64e7c5eb)
- [Dev Deps] update `auto-changelog`, `tape` [`6a01da3`](https://github.com/es-shims/globalThis/commit/6a01da3f321983d1970d793711d31cf8508ef94d)
- [Dev Deps] update `@ljharb/eslint-config`, `tape` [`7a07f4e`](https://github.com/es-shims/globalThis/commit/7a07f4ebc5580933b40bbe67f357632e0f7d5586)
- [meta] only run the build script in publish [`797e492`](https://github.com/es-shims/globalThis/commit/797e492519ed0bf6270537290e69ca0456790575)
- [meta] combine duplicate `prepublish` scripts [`92bbef0`](https://github.com/es-shims/globalThis/commit/92bbef0f91f6e91163186f68b5f5f1ffd26c479d)
- [Dev Deps] update `auto-changelog`; add `aud` [`be6dbec`](https://github.com/es-shims/globalThis/commit/be6dbecefddb40493c5568a2cbe83f74e2e0385f)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`bfd54f8`](https://github.com/es-shims/globalThis/commit/bfd54f8388758e7dec618dc34956e7075a7c15f0)
- [Tests] only audit prod deps [`0f64b47`](https://github.com/es-shims/globalThis/commit/0f64b47acfa812affbacbe487fcb0f6c02eccc25)

## [v1.0.1](https://github.com/es-shims/globalThis/compare/v1.0.0...v1.0.1) - 2019-12-15

### Fixed

- [Refactor] only use `global` in node; only check browser globals in browsers [`#2`](https://github.com/es-shims/globalThis/issues/2)

### Commits

- [Tests] use shared travis-ci configs [`edb1cc9`](https://github.com/es-shims/globalThis/commit/edb1cc9d900a40e8c1732264b6e85d4f9760920c)
- [Tests] remove `jscs` [`1847ac2`](https://github.com/es-shims/globalThis/commit/1847ac2487e2c13cf8bf717211c6a93fe60831f9)
- [meta] add `auto-changelog` [`933c381`](https://github.com/es-shims/globalThis/commit/933c381083890965ac848d3da21ed9e910cc09cf)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `browserify`, `tape` [`93310bc`](https://github.com/es-shims/globalThis/commit/93310bc01ddacbe23a93b3022daebc9b6f6ae8c3)
- [actions] add automatic rebasing / merge commit blocking [`231dec5`](https://github.com/es-shims/globalThis/commit/231dec511c42e1509035d176e2451c55de20bfe7)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `browserify`, `covert`, `is`, `tape` [`e50c1f6`](https://github.com/es-shims/globalThis/commit/e50c1f6d2d45c66f53ffda471bbf62c08ed15c9b)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`4abd340`](https://github.com/es-shims/globalThis/commit/4abd3400fc8942963e77515d0cf2fbcac3cb7bc8)
- [meta] add `funding` field [`2d1f9eb`](https://github.com/es-shims/globalThis/commit/2d1f9eb00b2dea46f6de7d563b31db17f44f1899)
- [meta] remove unused deps [`5bd6bef`](https://github.com/es-shims/globalThis/commit/5bd6befefbaf0c7e6f70eb3c1919b5c5a271d29d)
- readme: Fix casing + phrasing [`66379cc`](https://github.com/es-shims/globalThis/commit/66379ccf5008f7676aac5f3dec1ea2fe55e3516c)
- [Deps] update `define-properties`, `object-keys` [`4585e5a`](https://github.com/es-shims/globalThis/commit/4585e5ab461093ab6c62ce0b22b959925e8f818c)
- fix issue with Webpack's CaseSensitivePathsPlugin [`842e84e`](https://github.com/es-shims/globalThis/commit/842e84e0096c9eea660c78fd19c9c07799b81537)

## v1.0.0 - 2018-08-10

### Commits

- Dotfiles. [`f01b02d`](https://github.com/es-shims/globalThis/commit/f01b02d315865c812e5b9158f71bb18f3b153def)
- [Tests] up to `node` `v10.7`, `v9.11`, `v8.11`, `v7.10`, `v6.14`, `v4.9`; use `nvm install-latest-npm`; improve matrix [`ed1fa5d`](https://github.com/es-shims/globalThis/commit/ed1fa5d473d933b3270410b658183dc1c556a663)
- Tests [`ab99527`](https://github.com/es-shims/globalThis/commit/ab99527e3c434e89dd40f8cba3b0e2e976156611)
- [breaking] update property name, rename repo [`be42e3d`](https://github.com/es-shims/globalThis/commit/be42e3dce08b62a78260d487f62fa69b410d7918)
- package.json [`ca43a36`](https://github.com/es-shims/globalThis/commit/ca43a363e3ce0dbc2d4623169f8cb3d792f8bc84)
- implementation [`80b5a40`](https://github.com/es-shims/globalThis/commit/80b5a403ef532254b2af46ec3ba5f442a308a57d)
- read me [`f6df9b3`](https://github.com/es-shims/globalThis/commit/f6df9b3b69977f04e080d1720ba1203c13447884)
- Rename `System.global` to `global` [`fa8503c`](https://github.com/es-shims/globalThis/commit/fa8503cf94afe84b3729dd5b0e9f73f481fb1fee)
- Initial commit [`99f1dc3`](https://github.com/es-shims/globalThis/commit/99f1dc328d0b4c52a550037de0139d5452ac01de)
- [Tests] up to `node` `v6.7`, `v5.12`, `v4.6`; improve test matrix [`712ec0e`](https://github.com/es-shims/globalThis/commit/712ec0e545d1603c4e23f4ff1acb066cc4a3c9ee)
- [Dev Deps] update `browserify`, `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`73278bd`](https://github.com/es-shims/globalThis/commit/73278bd638d1e762eb7415350a738f5d345896f5)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `browserify`, `eslint`, `for-each`, `is`, `nsp`, `tape` [`75fa992`](https://github.com/es-shims/globalThis/commit/75fa9929be81afec43895c02e33d0b8a78f11d1f)
- [Dev Deps] update `browserify`, `is`, `tape`, `nsp`, `eslint` [`b223e86`](https://github.com/es-shims/globalThis/commit/b223e86d0868efb1f0c966370ff2f822516d6956)
- [Tests] fix linting; remove parallelshell [`271b329`](https://github.com/es-shims/globalThis/commit/271b329d174b94c08913060752a2e9f9116fe5b8)
- [Deps] update `function-bind`, `object-keys` [`002d0c5`](https://github.com/es-shims/globalThis/commit/002d0c5685a83f97e014a8a07134eb621794c649)
- Only apps should have lockfiles [`960f1d0`](https://github.com/es-shims/globalThis/commit/960f1d00598cbba5427849c863eb10b8de82fb1b)
- [Tests] on `node` `v10.8` [`37fad9d`](https://github.com/es-shims/globalThis/commit/37fad9db9860c654efe0a32ec187f21730d5fed8)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`df28dfe`](https://github.com/es-shims/globalThis/commit/df28dfe7f0daf3db95a536a6ce64062bd706185d)
- [New] add `auto` entry point [`86eb2ab`](https://github.com/es-shims/globalThis/commit/86eb2ab4c4dc2babff20ac436cf7fb7f8da7d2f2)
- [Dev Deps] update `eslint` [`1bdc1aa`](https://github.com/es-shims/globalThis/commit/1bdc1aacfb94dcdc7bb61688c7634c435012e35d)
- [Deps] update `object-keys` [`72cdbf5`](https://github.com/es-shims/globalThis/commit/72cdbf596b16103ee711d52b2b645b42efc08c51)
- Update most common usage to invoke the function upon being required [`5026296`](https://github.com/es-shims/globalThis/commit/502629660da2c21cfb0f8ca233e2b9d427c052fe)
