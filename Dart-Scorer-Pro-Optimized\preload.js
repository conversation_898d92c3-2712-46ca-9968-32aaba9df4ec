// preload.js
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object.
contextBridge.exposeInMainWorld('electronAPI', {
  // Example: expose a function to send messages to main process
  // send: (channel, data) => ipcRenderer.send(channel, data),
  // invoke: (channel, data) => ipcRenderer.invoke(channel, data),
  // on: (channel, func) => {
  //   const validChannels = ['window-resized']; // Define valid channels
  //   if (validChannels.includes(channel)) {
  //     // Deliberately strip event as it includes `sender`
  //     ipcRenderer.on(channel, (event, ...args) => func(...args));
  //   }
  // },
  // removeListener: (channel, func) => ipcRenderer.removeListener(channel, func),
  
  // If you need to pass Firebase config or app ID from main to renderer securely,
  // you could use ipc<PERSON>enderer.invoke here, though for this app,
  // we'll assume __firebase_config and __app_id are available globally in renderer via other means (e.g. injected by build process or directly in HTML if safe).
  // For a production app, using IPC to fetch sensitive config from main process is safer.
});

// All Node.js APIs are available in the preload process.
// It has the same sandbox as a Chrome extension.
window.addEventListener('DOMContentLoaded', () => {
  // You can put DOM manipulation code here that needs to run
  // before the renderer's main script.
  // For example, setting the app version from package.json
  // const replaceText = (selector, text) => {
  //   const element = document.getElementById(selector);
  //   if (element) element.innerText = text;
  // };
  // replaceText('app-version', process.versions.app); // Assuming you have <span id="app-version"></span>
});
