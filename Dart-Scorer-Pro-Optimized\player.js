// player.js

export function createPlayer(name) {
    return {
        id: `player_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
        name: name,
        currentScore: 0, // Will be set at game start
        throwsHistory: [], // [{ round: 1, darts: [{score: 20, multiplier: 1, text:'S20', value:20}, ...], turnScore: X, scoreBeforeTurn: Y, scoreAfterTurn: Z, isBust: false}, ...]
        dartsThisTurn: [], // [{score: 20, multiplier: 1, text:'S20', value:20}, ...] temporary for current turn
        stats: resetPlayerStats(),
    };
}

export function rehydratePlayer(playerData) {
    // If player objects have methods or need specific setup upon loading, do it here.
    // For this simple structure, spreading might be enough.
    return {
        ...playerData,
        // Ensure dartsThisTurn is initialized if not present in saved data (it's transient)
        dartsThisTurn: playerData.dartsThisTurn || [], 
        // Ensure stats object exists
        stats: playerData.stats || resetPlayerStats(),
        // Ensure throwsHistory is an array
        throwsHistory: Array.isArray(playerData.throwsHistory) ? playerData.throwsHistory : [],
    };
}


export function addPlayer(players, name) {
    const newPlayer = createPlayer(name);
    return [...players, newPlayer];
}

export function removePlayer(players, playerNameOrId) {
    return players.filter(p => p.name !== playerNameOrId && p.id !== playerNameOrId);
}

export function resetPlayerStats(existingStats = {}) {
     return {
        totalThrows: 0,
        totalScoreThrown: 0,
        averageScorePerTurn: 0, // Calculated at end of game or on demand
        averageScorePerDart: 0, // Calculated
        highestTurnScore: 0,
        doublesHit: 0,
        triplesHit: 0,
        oneEighties: 0, // Count of 180 scores
        gamesWon: existingStats.gamesWon || 0, // Persist games won across sessions if desired
        legsWon: existingStats.legsWon || 0,
        // Add more stats as needed
    };
}

export function calculateAverageStats(player) {
    if (!player || !player.throwsHistory || player.throwsHistory.length === 0) {
        player.stats.averageScorePerTurn = 0;
        player.stats.averageScorePerDart = 0;
        return;
    }

    let totalEffectiveTurns = 0;
    let totalEffectiveScore = 0;
    let totalDartsThrownInHistory = 0;

    player.throwsHistory.forEach(turn => {
        if (!turn.isBust) {
            totalEffectiveScore += turn.turnScore;
            totalEffectiveTurns++;
        }
        totalDartsThrownInHistory += turn.darts.length;
    });
    
    player.stats.averageScorePerTurn = totalEffectiveTurns > 0 ? (totalEffectiveScore / totalEffectiveTurns) : 0;
    // Use player.stats.totalThrows for average per dart, as it counts all darts including bust turns
    player.stats.averageScorePerDart = player.stats.totalThrows > 0 ? (player.stats.totalScoreThrown / player.stats.totalThrows) : 0;
}
