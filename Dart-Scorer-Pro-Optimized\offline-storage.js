// offline-storage.js
// Local storage implementation for offline game saving

const STORAGE_PREFIX = 'dartScorer_';
const GAMES_KEY = STORAGE_PREFIX + 'savedGames';
const SETTINGS_KEY = STORAGE_PREFIX + 'settings';
const MAX_SAVED_GAMES = 20; // Allow more games for offline storage

// Generate a unique user ID for this browser/device
function getOrCreateUserId() {
    const userIdKey = STORAGE_PREFIX + 'userId';
    let userId = localStorage.getItem(userIdKey);
    
    if (!userId) {
        // Generate a unique ID for this browser/device
        userId = 'offline_' + Date.now() + '_' + Math.random().toString(36).substring(2, 15);
        localStorage.setItem(userIdKey, userId);
    }
    
    return userId;
}

// Get all saved games from localStorage
function getAllSavedGames() {
    try {
        const gamesData = localStorage.getItem(GAMES_KEY);
        return gamesData ? JSON.parse(gamesData) : {};
    } catch (error) {
        console.error('Error reading saved games from localStorage:', error);
        return {};
    }
}

// Save all games to localStorage
function saveAllGames(gamesData) {
    try {
        localStorage.setItem(GAMES_KEY, JSON.stringify(gamesData));
        return true;
    } catch (error) {
        console.error('Error saving games to localStorage:', error);
        
        // Handle quota exceeded error
        if (error.name === 'QuotaExceededError') {
            console.warn('localStorage quota exceeded, attempting to clean up old games...');
            cleanupOldGames();
            try {
                localStorage.setItem(GAMES_KEY, JSON.stringify(gamesData));
                return true;
            } catch (retryError) {
                console.error('Failed to save even after cleanup:', retryError);
                return false;
            }
        }
        return false;
    }
}

// Clean up old games to free space
function cleanupOldGames() {
    const gamesData = getAllSavedGames();
    const gameEntries = Object.entries(gamesData);
    
    // Sort by savedAt timestamp (newest first)
    gameEntries.sort((a, b) => {
        const timeA = a[1].savedAt || 0;
        const timeB = b[1].savedAt || 0;
        return timeB - timeA;
    });
    
    // Keep only the newest MAX_SAVED_GAMES
    const gamesToKeep = gameEntries.slice(0, MAX_SAVED_GAMES);
    const cleanedGames = Object.fromEntries(gamesToKeep);
    
    localStorage.setItem(GAMES_KEY, JSON.stringify(cleanedGames));
    console.log(`Cleaned up old games, kept ${gamesToKeep.length} most recent games`);
}

// --- Public API Functions ---

export async function saveGame(gameState) {
    try {
        const gameToSave = {
            ...gameState,
            players: gameState.players.map(p => ({
                id: p.id,
                name: p.name,
                currentScore: p.currentScore,
                throwsHistory: p.throwsHistory || [],
                dartsThisTurn: [], // Don't save current turn darts
                stats: p.stats || {}
            })),
            savedAt: Date.now(), // Use timestamp instead of serverTimestamp
            userId: getOrCreateUserId(),
            version: '1.0' // For future compatibility
        };
        
        // Generate game ID if not exists
        const gameId = gameState.id || `game_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
        
        // Get all saved games
        const allGames = getAllSavedGames();
        
        // Add/update this game
        allGames[gameId] = gameToSave;
        
        // Save back to localStorage
        const success = saveAllGames(allGames);
        
        if (success) {
            console.log('Game saved successfully to offline storage with ID:', gameId);
            return gameId;
        } else {
            throw new Error('Failed to save game to localStorage');
        }
        
    } catch (error) {
        console.error('Error saving game to offline storage:', error);
        throw error;
    }
}

export async function loadGame(gameId) {
    try {
        const allGames = getAllSavedGames();
        const gameData = allGames[gameId];
        
        if (gameData) {
            console.log('Game loaded successfully from offline storage:', gameId);
            return gameData;
        } else {
            console.warn('No game found with ID:', gameId);
            return null;
        }
        
    } catch (error) {
        console.error('Error loading game from offline storage:', error);
        throw error;
    }
}

export async function listSavedGames(callback) {
    try {
        const allGames = getAllSavedGames();
        const gameEntries = Object.entries(allGames);
        
        // Sort by savedAt timestamp (newest first)
        gameEntries.sort((a, b) => {
            const timeA = a[1].savedAt || 0;
            const timeB = b[1].savedAt || 0;
            return timeB - timeA;
        });
        
        // Extract game IDs with additional info for display
        const gameList = gameEntries.slice(0, MAX_SAVED_GAMES).map(([gameId, gameData]) => {
            const savedDate = new Date(gameData.savedAt);
            const playerNames = gameData.players ? gameData.players.map(p => p.name).join(', ') : 'Unknown';
            const gameMode = gameData.settings ? gameData.settings.startingScore : 'Unknown';
            
            return {
                id: gameId,
                displayName: `${gameMode} Game - ${playerNames} (${savedDate.toLocaleDateString()})`,
                savedAt: gameData.savedAt,
                players: playerNames,
                gameMode: gameMode
            };
        });
        
        if (callback) {
            callback(gameList.map(g => g.id)); // For compatibility with existing code
        }
        
        return gameList;
        
    } catch (error) {
        console.error('Error listing saved games from offline storage:', error);
        if (callback) callback([]);
        return [];
    }
}

export async function deleteGame(gameId) {
    try {
        const allGames = getAllSavedGames();
        
        if (allGames[gameId]) {
            delete allGames[gameId];
            const success = saveAllGames(allGames);
            
            if (success) {
                console.log('Game deleted successfully:', gameId);
                return true;
            } else {
                throw new Error('Failed to save after deletion');
            }
        } else {
            console.warn('Game not found for deletion:', gameId);
            return false;
        }
        
    } catch (error) {
        console.error('Error deleting game from offline storage:', error);
        throw error;
    }
}

export async function exportGames() {
    try {
        const allGames = getAllSavedGames();
        const exportData = {
            version: '1.0',
            exportedAt: Date.now(),
            userId: getOrCreateUserId(),
            games: allGames
        };
        
        return JSON.stringify(exportData, null, 2);
        
    } catch (error) {
        console.error('Error exporting games:', error);
        throw error;
    }
}

export async function importGames(jsonData) {
    try {
        const importData = JSON.parse(jsonData);
        
        if (!importData.games) {
            throw new Error('Invalid import data format');
        }
        
        const currentGames = getAllSavedGames();
        const importedGames = importData.games;
        
        // Merge imported games with current games
        // Imported games will overwrite existing ones with same ID
        const mergedGames = { ...currentGames, ...importedGames };
        
        const success = saveAllGames(mergedGames);
        
        if (success) {
            const importedCount = Object.keys(importedGames).length;
            console.log(`Successfully imported ${importedCount} games`);
            return importedCount;
        } else {
            throw new Error('Failed to save imported games');
        }
        
    } catch (error) {
        console.error('Error importing games:', error);
        throw error;
    }
}

// Get storage usage information
export function getStorageInfo() {
    try {
        const allGames = getAllSavedGames();
        const gameCount = Object.keys(allGames).length;
        const dataSize = JSON.stringify(allGames).length;
        const userId = getOrCreateUserId();
        
        return {
            gameCount,
            dataSizeBytes: dataSize,
            dataSizeKB: Math.round(dataSize / 1024),
            userId,
            maxGames: MAX_SAVED_GAMES
        };
        
    } catch (error) {
        console.error('Error getting storage info:', error);
        return {
            gameCount: 0,
            dataSizeBytes: 0,
            dataSizeKB: 0,
            userId: 'unknown',
            maxGames: MAX_SAVED_GAMES
        };
    }
}

// Clear all saved games (with confirmation)
export async function clearAllGames() {
    try {
        localStorage.removeItem(GAMES_KEY);
        console.log('All saved games cleared from offline storage');
        return true;
    } catch (error) {
        console.error('Error clearing saved games:', error);
        return false;
    }
}

// Check if offline storage is available
export function isOfflineStorageAvailable() {
    try {
        const testKey = STORAGE_PREFIX + 'test';
        localStorage.setItem(testKey, 'test');
        localStorage.removeItem(testKey);
        return true;
    } catch (error) {
        console.error('localStorage not available:', error);
        return false;
    }
}
