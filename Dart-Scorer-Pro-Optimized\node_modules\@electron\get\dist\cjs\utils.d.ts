export declare function withTempDirectoryIn<T>(parentDirectory: string | undefined, fn: (directory: string) => Promise<T>): Promise<T>;
export declare function withTempDirectory<T>(fn: (directory: string) => Promise<T>): Promise<T>;
export declare function normalizeVersion(version: string): string;
/**
 * Runs the `uname` command and returns the trimmed output.
 */
export declare function uname(): string;
/**
 * Generates an architecture name that would be used in an Electron or Node.js
 * download file name.
 */
export declare function getNodeArch(arch: string): string;
/**
 * Generates an architecture name that would be used in an Electron or Node.js
 * download file name, from the `process` module information.
 */
export declare function getHostArch(): string;
export declare function ensureIsTruthyString<T, K extends keyof T>(obj: T, key: K): void;
export declare function isOfficialLinuxIA32Download(platform: string, arch: string, version: string, mirrorOptions?: object): boolean;
/**
 * Find the value of a environment variable which may or may not have the
 * prefix, in a case-insensitive manner.
 */
export declare function getEnv(prefix?: string): (name: string) => string | undefined;
export declare function setEnv(key: string, value: string | undefined): void;
